#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/openai@5.5.1_zod@3.25.67/node_modules/openai/bin/node_modules:/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/openai@5.5.1_zod@3.25.67/node_modules/openai/node_modules:/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/openai@5.5.1_zod@3.25.67/node_modules:/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/openai@5.5.1_zod@3.25.67/node_modules/openai/bin/node_modules:/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/openai@5.5.1_zod@3.25.67/node_modules/openai/node_modules:/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/openai@5.5.1_zod@3.25.67/node_modules:/home/<USER>/Desktop/vendor_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/openai@5.5.1_zod@3.25.67/node_modules/openai/bin/cli" "$@"
else
  exec node  "$basedir/../.pnpm/openai@5.5.1_zod@3.25.67/node_modules/openai/bin/cli" "$@"
fi
