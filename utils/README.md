# HTTP Client Utility

A comprehensive HTTP client utility built on top of axios for the vendor_ai project. This utility provides a consistent interface for making HTTP requests with built-in error handling, response formatting, and configuration management.

## Features

- ✅ **Comprehensive HTTP Methods**: GET, POST, PUT, PATCH, DELETE
- ✅ **Error Handling**: Automatic error formatting and categorization
- ✅ **Response Formatting**: Consistent response structure with validation
- ✅ **Request/Response Logging**: Built-in logging for debugging
- ✅ **Authentication Support**: Easy token management
- ✅ **Configurable**: Customizable timeouts, headers, and base URLs
- ✅ **Validation**: Response validation using Zod schemas
- ✅ **TypeScript Ready**: JSDoc comments for better IDE support

## Installation

The HTTP client utility is already included in the project. Make sure you have the required dependencies:

```bash
pnpm install axios zod
```

## Quick Start

### Using Convenience Functions

```javascript
const { get, post, put, patch, delete: del } = require('./utils/httpClient');

// GET request
const users = await get('https://api.example.com/users');
console.log(users.data);

// POST request
const newUser = await post('https://api.example.com/users', {
  name: 'John Doe',
  email: '<EMAIL>'
});

// PUT request
const updatedUser = await put('https://api.example.com/users/1', {
  name: 'Jane Doe',
  email: '<EMAIL>'
});

// PATCH request
const patchedUser = await patch('https://api.example.com/users/1', {
  email: '<EMAIL>'
});

// DELETE request
await del('https://api.example.com/users/1');
```

### Using HttpClient Class

```javascript
const { HttpClient } = require('./utils/httpClient');

// Create a configured client
const apiClient = new HttpClient({
  baseURL: 'https://api.example.com',
  timeout: 10000,
  headers: {
    'X-API-Key': 'your-api-key',
    'X-Client-Version': '1.0.0'
  }
});

// Set authentication
apiClient.setAuthToken('your-jwt-token');

// Make requests
const response = await apiClient.get('/users');
```

## API Reference

### HttpClient Class

#### Constructor

```javascript
new HttpClient(config)
```

**Parameters:**
- `config` (Object): Axios configuration object
  - `baseURL` (string): Base URL for requests
  - `timeout` (number): Request timeout in milliseconds (default: 10000)
  - `headers` (Object): Default headers

#### Methods

##### HTTP Methods

- `get(url, config)` - Perform GET request
- `post(url, data, config)` - Perform POST request
- `put(url, data, config)` - Perform PUT request
- `patch(url, data, config)` - Perform PATCH request
- `delete(url, config)` - Perform DELETE request

##### Configuration Methods

- `setAuthToken(token, type)` - Set authorization header
- `clearAuthToken()` - Remove authorization header
- `setBaseURL(baseURL)` - Update base URL
- `setHeaders(headers)` - Update default headers

### Response Format

All successful responses follow this structure:

```javascript
{
  success: true,
  data: any,           // Response data
  status: number,      // HTTP status code
  statusText: string,  // HTTP status text
  headers: Object,     // Response headers
  timestamp: string    // ISO timestamp
}
```

### Error Format

All errors follow this structure:

```javascript
{
  success: false,
  message: string,     // Error message
  status?: number,     // HTTP status (if available)
  statusText?: string, // HTTP status text (if available)
  data?: any,          // Error response data (if available)
  headers?: Object,    // Response headers (if available)
  code?: string,       // Error code (NETWORK_ERROR, REQUEST_SETUP_ERROR)
  timestamp: string    // ISO timestamp
}
```

## Error Handling

The HTTP client categorizes errors into three types:

1. **Server Errors** (4xx, 5xx): Server responded with an error status
2. **Network Errors**: Request was made but no response received
3. **Request Setup Errors**: Error in request configuration

```javascript
try {
  const response = await get('https://api.example.com/users');
  console.log(response.data);
} catch (error) {
  if (error.status === 404) {
    console.log('Resource not found');
  } else if (error.code === 'NETWORK_ERROR') {
    console.log('Network connection failed');
  } else {
    console.log('Other error:', error.message);
  }
}
```

## Configuration Examples

### API Client with Authentication

```javascript
const apiClient = new HttpClient({
  baseURL: 'https://api.vendor-ai.com',
  timeout: 15000,
  headers: {
    'X-API-Version': '2.0',
    'X-Client-ID': 'vendor_ai_client'
  }
});

// Set JWT token
apiClient.setAuthToken('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');

// Make authenticated requests
const profile = await apiClient.get('/profile');
const orders = await apiClient.get('/orders');
```

### Multiple API Clients

```javascript
// Main API client
const mainAPI = new HttpClient({
  baseURL: 'https://api.vendor-ai.com',
  headers: { 'X-Service': 'main' }
});

// Analytics API client
const analyticsAPI = new HttpClient({
  baseURL: 'https://analytics.vendor-ai.com',
  headers: { 'X-Service': 'analytics' }
});

// Payment API client
const paymentAPI = new HttpClient({
  baseURL: 'https://payments.vendor-ai.com',
  headers: { 'X-Service': 'payments' }
});
```

## Testing

Run the included tests to verify functionality:

```bash
node tests/httpClient.test.js
```

Run the examples to see usage patterns:

```bash
node examples/httpClientExamples.js
```

## Best Practices

1. **Use the HttpClient class** for applications that need multiple configurations
2. **Use convenience functions** for simple, one-off requests
3. **Always handle errors** appropriately for your use case
4. **Set appropriate timeouts** based on your API requirements
5. **Use authentication methods** instead of manually setting headers
6. **Log requests** in development but consider privacy in production

## Dependencies

- **axios**: HTTP client library
- **zod**: Schema validation library

## License

This utility is part of the vendor_ai project and follows the same license terms.
