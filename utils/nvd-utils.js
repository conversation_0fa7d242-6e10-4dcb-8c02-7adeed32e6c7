const { axiosClient } = require("./client"); // Assuming your client is in a separate file

/**
 * NVD API Client - Reusable functions for interacting with the National Vulnerability Database
 */

/**
 * Get CVE vulnerabilities with various filtering options
 * @param {Object} params - Query parameters
 * @param {string} [params.cveId] - Specific CVE ID (e.g., "CVE-2019-1010218")
 * @param {string} [params.cpeName] - CPE name to filter by
 * @param {string} [params.cveTag] - CVE tag filter (disputed, unsupported-when-assigned, exclusively-hosted-service)
 * @param {string} [params.cvssV2Metrics] - CVSSv2 vector string
 * @param {string} [params.cvssV2Severity] - CVSSv2 severity (LOW, MEDIUM, HIGH)
 * @param {string} [params.cvssV3Metrics] - CVSSv3 vector string
 * @param {string} [params.cvssV3Severity] - CVSSv3 severity (LOW, MEDIUM, HIGH, CRITICAL)
 * @param {string} [params.cvssV4Metrics] - CVSSv4 vector string
 * @param {string} [params.cvssV4Severity] - CVSSv4 severity (LOW, MEDIUM, HIGH, CRITICAL)
 * @param {string} [params.cweId] - CWE ID (e.g., "CWE-287")
 * @param {boolean} [params.hasCertAlerts] - Filter for CVEs with CERT alerts
 * @param {boolean} [params.hasCertNotes] - Filter for CVEs with CERT notes
 * @param {boolean} [params.hasKev] - Filter for CVEs in CISA's KEV catalog
 * @param {boolean} [params.hasOval] - Filter for CVEs with OVAL data
 * @param {boolean} [params.isVulnerable] - Filter for vulnerable CPEs (requires cpeName)
 * @param {string} [params.keywordSearch] - Keyword search in descriptions
 * @param {boolean} [params.keywordExactMatch] - Exact phrase matching (requires keywordSearch)
 * @param {string} [params.lastModStartDate] - Last modified start date (ISO 8601)
 * @param {string} [params.lastModEndDate] - Last modified end date (ISO 8601)
 * @param {boolean} [params.noRejected] - Exclude rejected CVEs
 * @param {string} [params.pubStartDate] - Published start date (ISO 8601)
 * @param {string} [params.pubEndDate] - Published end date (ISO 8601)
 * @param {number} [params.resultsPerPage=2000] - Number of results per page (max 2000)
 * @param {number} [params.startIndex=0] - Starting index for pagination
 * @param {string} [params.sourceIdentifier] - Source identifier
 * @param {string} [params.virtualMatchString] - Virtual CPE match string
 * @param {string} [params.versionStart] - Starting version for range
 * @param {string} [params.versionStartType] - Version start type (including, excluding)
 * @param {string} [params.versionEnd] - Ending version for range
 * @param {string} [params.versionEndType] - Version end type (including, excluding)
 * @returns {Promise<Object>} CVE data response
 */
async function getCVEs(params = {}) {
  try {
    const response = await axiosClient.get("/cves/2.0", { params });
    return response.data;
  } catch (error) {
    throw new Error(`Failed to fetch CVEs: ${error.message}`);
  }
}

/**
 * Get a specific CVE by ID
 * @param {string} cveId - CVE identifier (e.g., "CVE-2019-1010218")
 * @returns {Promise<Object>} Single CVE data
 */
async function getCVEById(cveId) {
  if (!cveId) {
    throw new Error("CVE ID is required");
  }

  try {
    const response = await getCVEs({ cveId });
    return response.vulnerabilities?.[0] || null;
  } catch (error) {
    throw new Error(`Failed to fetch CVE ${cveId}: ${error.message}`);
  }
}

/**
 * Search CVEs by keyword
 * @param {string} keyword - Search keyword or phrase
 * @param {boolean} [exactMatch=false] - Whether to match exact phrase
 * @param {number} [limit=100] - Maximum results to return
 * @returns {Promise<Object>} Search results
 */
async function searchCVEsByKeyword(keyword, exactMatch = false, limit = 100) {
  if (!keyword) {
    throw new Error("Keyword is required");
  }

  const params = {
    keywordSearch: keyword,
    resultsPerPage: Math.min(limit, 2000),
  };

  if (exactMatch) {
    params.keywordExactMatch = true;
  }

  return getCVEs(params);
}

/**
 * Get CVEs by severity level
 * @param {string} severity - Severity level (LOW, MEDIUM, HIGH, CRITICAL)
 * @param {string} [version='v3'] - CVSS version (v2, v3, v4)
 * @param {number} [limit=100] - Maximum results to return
 * @returns {Promise<Object>} CVEs filtered by severity
 */
async function getCVEsBySeverity(severity, version = "v3", limit = 100) {
  const validSeverities = {
    v2: ["LOW", "MEDIUM", "HIGH"],
    v3: ["LOW", "MEDIUM", "HIGH", "CRITICAL"],
    v4: ["LOW", "MEDIUM", "HIGH", "CRITICAL"],
  };

  if (!validSeverities[version]?.includes(severity.toUpperCase())) {
    throw new Error(`Invalid severity ${severity} for CVSS ${version}`);
  }

  const params = {
    resultsPerPage: Math.min(limit, 2000),
  };

  params[`cvss${version.toUpperCase()}Severity`] = severity.toUpperCase();

  return getCVEs(params);
}

/**
 * Get CVEs by date range
 * @param {string} startDate - Start date (ISO 8601 format)
 * @param {string} endDate - End date (ISO 8601 format)
 * @param {string} [dateType='published'] - Date type ('published' or 'modified')
 * @param {number} [limit=1000] - Maximum results to return
 * @returns {Promise<Object>} CVEs in date range
 */
async function getCVEsByDateRange(
  startDate,
  endDate,
  dateType = "published",
  limit = 1000
) {
  if (!startDate || !endDate) {
    throw new Error("Both start and end dates are required");
  }

  const params = {
    resultsPerPage: Math.min(limit, 2000),
  };

  if (dateType === "published") {
    params.pubStartDate = startDate;
    params.pubEndDate = endDate;
  } else if (dateType === "modified") {
    params.lastModStartDate = startDate;
    params.lastModEndDate = endDate;
  } else {
    throw new Error('Date type must be "published" or "modified"');
  }

  return getCVEs(params);
}

/**
 * Get CVEs associated with a specific CPE
 * @param {string} cpeName - CPE name string
 * @param {boolean} [vulnerableOnly=false] - Only return vulnerable configurations
 * @param {number} [limit=100] - Maximum results to return
 * @returns {Promise<Object>} CVEs associated with CPE
 */
async function getCVEsByCPE(cpeName, vulnerableOnly = false, limit = 100) {
  if (!cpeName) {
    throw new Error("CPE name is required");
  }

  const params = {
    cpeName,
    resultsPerPage: Math.min(limit, 2000),
  };

  if (vulnerableOnly) {
    params.isVulnerable = true;
  }

  return getCVEs(params);
}

/**
 * Get recent CVEs (last 7 days by default)
 * @param {number} [days=7] - Number of days to look back
 * @param {number} [limit=100] - Maximum results to return
 * @returns {Promise<Object>} Recent CVEs
 */
async function getRecentCVEs(days = 7, limit = 100) {
  const endDate = new Date().toISOString();
  const startDate = new Date(
    Date.now() - days * 24 * 60 * 60 * 1000
  ).toISOString();

  return getCVEsByDateRange(startDate, endDate, "published", limit);
}

/**
 * Get CVEs by CWE (Common Weakness Enumeration)
 * @param {string} cweId - CWE identifier (e.g., "CWE-79", "CWE-89")
 * @param {number} [limit=100] - Maximum results to return
 * @returns {Promise<Object>} CVEs with specified weakness
 */
async function getCVEsByCWE(cweId, limit = 100) {
  if (!cweId) {
    throw new Error("CWE ID is required");
  }

  return getCVEs({
    cweId,
    resultsPerPage: Math.min(limit, 2000),
  });
}

/**
 * Get CVE change history
 * @param {Object} params - Query parameters
 * @param {string} [params.cveId] - Specific CVE ID
 * @param {string} [params.changeStartDate] - Change start date (ISO 8601)
 * @param {string} [params.changeEndDate] - Change end date (ISO 8601)
 * @param {string} [params.eventName] - Event name filter
 * @param {number} [params.resultsPerPage=1000] - Results per page (max 5000)
 * @param {number} [params.startIndex=0] - Starting index
 * @returns {Promise<Object>} CVE change history
 */
async function getCVEChangeHistory(params = {}) {
  try {
    const response = await axiosClient.get("/cvehistory/2.0", { params });
    return response.data;
  } catch (error) {
    throw new Error(`Failed to fetch CVE history: ${error.message}`);
  }
}

/**
 * Get change history for a specific CVE
 * @param {string} cveId - CVE identifier
 * @returns {Promise<Object>} Change history for the CVE
 */
async function getCVEChangeHistoryById(cveId) {
  if (!cveId) {
    throw new Error("CVE ID is required");
  }

  return getCVEChangeHistory({ cveId });
}

/**
 * Paginate through all results for a query
 * @param {Function} queryFunction - Function that accepts params and returns paginated results
 * @param {Object} baseParams - Base parameters for the query
 * @param {number} [maxResults=10000] - Maximum total results to fetch
 * @returns {Promise<Array>} All results from pagination
 */
async function paginateResults(
  queryFunction,
  baseParams = {},
  maxResults = 10000
) {
  const allResults = [];
  let startIndex = 0;
  const resultsPerPage = 2000; // Maximum allowed

  try {
    while (allResults.length < maxResults) {
      const params = {
        ...baseParams,
        startIndex,
        resultsPerPage,
      };

      const response = await queryFunction(params);

      if (!response.vulnerabilities || response.vulnerabilities.length === 0) {
        break;
      }

      allResults.push(...response.vulnerabilities);

      // Check if we've got all results
      if (
        response.vulnerabilities.length < resultsPerPage ||
        allResults.length >= response.totalResults
      ) {
        break;
      }

      startIndex += resultsPerPage;
    }

    return allResults.slice(0, maxResults);
  } catch (error) {
    throw new Error(`Pagination failed: ${error.message}`);
  }
}

/**
 * Get high-severity CVEs with KEV (Known Exploited Vulnerabilities) flag
 * @param {number} [limit=50] - Maximum results to return
 * @returns {Promise<Object>} High-severity CVEs in KEV catalog
 */
async function getHighSeverityKEVCVEs(limit = 50) {
  return getCVEs({
    hasKev: true,
    cvssV3Severity: "HIGH",
    resultsPerPage: Math.min(limit, 2000),
  });
}

/**
 * Helper function to format date for API calls
 * @param {Date|string} date - Date to format
 * @returns {string} ISO 8601 formatted date string
 */
function formatDateForAPI(date) {
  if (typeof date === "string") {
    return new Date(date).toISOString();
  }
  return date.toISOString();
}

/**
 * Validate CVE ID format
 * @param {string} cveId - CVE ID to validate
 * @returns {boolean} True if valid CVE ID format
 */
function isValidCVEId(cveId) {
  const cvePattern = /^CVE-\d{4}-\d{4,}$/;
  return cvePattern.test(cveId);
}

module.exports = {
  // Main CVE API functions
  getCVEs,
  getCVEById,
  searchCVEsByKeyword,
  getCVEsBySeverity,
  getCVEsByDateRange,
  getCVEsByCPE,
  getRecentCVEs,
  getCVEsByCWE,

  // CVE Change History API functions
  getCVEChangeHistory,
  getCVEChangeHistoryById,

  // Utility functions
  paginateResults,
  getHighSeverityKEVCVEs,
  formatDateForAPI,
  isValidCVEId,
};
